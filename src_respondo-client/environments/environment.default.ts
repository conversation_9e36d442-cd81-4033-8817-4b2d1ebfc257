export const environment = {
    production: false,
    internalMode: false,
    appName: '[B] 5 ways',
    apiClientId: 'd551c54c43dcb06776333346fee1ede3',
    apiUrl: 'https://beta.api.client.5ways.pl/rest/',
    socketsUrl: 'https://beta.ws.5ways.pl',
    apiUrlRoot: 'https://beta.api.client.5ways.pl',
    respondoTermsUrl: 'https://respondo.s3.eu-central-1.amazonaws.com/regulamin_serwisu_07302021.pdf',
    guideUrl: 'http://beta.poradnikprzedsiebiorcy.pl',
    vatRate: 23,
    appVersion: require('../../package.json').version,
    appPrefix: '',
    simplifyPricing: true,
    cookieDomain: '5ways.pl', // zawsze dosotować do domeny aplikacji np localhost lub 5ways.local
    registerUrl: 'https://beta.accounts.5ways.pl/register',
    wFirmaOauth2: {
        clientId: '3ea5c6b61ca1e1dfdbbaa7f17a186775',
        authEndpoint: 'https://beta.wfirma.pl/oauth2/auth',
        redirectUrl: 'https://beta.client.5ways.pl',
        authCodeEndpoint: 'https://beta.api.client.5ways.pl/rest/wfirma_oauth2'
    },
    accountsUrl: 'https://beta.accounts.5ways.pl', // adres sso
    appUrl: 'https://beta.5ways.pl/'
};

/*
 * In development mode, to ignore zone related error stack frames such as
 * `zone.run`, `zoneDelegate.invokeTask` for easier debugging, you can
 * import the following file, but please comment it out in production mode
 * because it will have performance impact when throw error
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
