import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {IsMobileService} from '../../../../src_team/app/services/is-mobile.service';
import { NgIf, NgSwitch, NgSwitchCase, AsyncPipe } from '@angular/common';
import { <PERSON><PERSON>utton, MatMiniFabButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-issue-action-button',
    templateUrl: './issue-action-button.component.html',
    styleUrls: ['./issue-action-button.component.scss'],
    imports: [NgIf, MatButton, MatMiniFabButton, NgSwitch, NgSwitchCase, MatIcon, MatProgressSpinner, AsyncPipe, TranslatePipe]
})
export class IssueActionButtonComponent implements OnInit {
    @Input() actionType: 'sendIssue' | 'addIssue';
    @Input() disabled = false;
    @Input() sending = false;

    @Output() buttonClicked = new EventEmitter();

    color: string;
    colorMobile: string;

    constructor(public isMobileService: IsMobileService) {
    }

    ngOnInit() {
        switch (this.actionType) {
            case 'sendIssue':
                this.color = 'primary';
                this.colorMobile = 'primary';

                break;
            case 'addIssue':
                this.color = 'primary';
                this.colorMobile = 'primary';

                break;
        }
    }

    onClick(event) {
        this.buttonClicked.emit();
    }
}
