@use "../styles/variables" as *;

.issue-list-component {
    height: 100%;
    overflow: hidden;
    background-color: $light-grey;

    .issue-list {
        overflow-y: auto;
        padding: 20px;

        .headers-align .mat-expansion-panel-header-title,
        .headers-align .mat-expansion-panel-header-description {
            flex-basis: 0;
        }

        .headers-align .mat-expansion-panel-header-description {
            align-items: center;
            justify-content: space-between;
        }
    }

    .pagination {
        background-color: var(--background-color);
        min-height: 64px;
        padding-right: 12px;
        width: 100%;
    }

    .loading {
        height: 100%;
        width: 100%;
    }

    .no-data {
        font-size: 30px;
        font-weight: bold;
        height: 100%;
        letter-spacing: 2px;
        margin-top: 0;
        opacity: .2;
    }

    .issue-list-position {
        max-width: 1300px;
        margin: 0px auto;
        padding: 0 24px;
    }
}
