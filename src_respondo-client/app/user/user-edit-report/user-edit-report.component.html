<h1 mat-dialog-title>{{ 'USER-EDIT-REPORT.TITLE' | translate }}</h1>
<button class="transparent-button blue" mat-button>
    <mat-icon>edit</mat-icon>
    {{ 'USER-EDIT-REPORT.CHANGE-DATA-SOURCE' | translate }}
</button>
<button class="transparent-button blue" mat-button>
    <mat-icon>print</mat-icon>
    {{ 'USER-EDIT-REPORT.GENERATE-REPORT' | translate }}
</button>
<h4 class="report-title">
    {{ 'USER-EDIT-REPORT.DAILY-REPORT-TITLE' | translate: { day: currentDate | date:'dd-MM-yyyy' } }}
</h4>
<div class="user-edit-report">
    <mat-tab-group class="tabs">
        <mat-tab label="{{ 'USER-EDIT-REPORT.CURRENT-DATA' | translate }}">
            <div class="header">{{ 'USER-EDIT-REPORT.EMPLOYEE-DATA' | translate }}</div>
            <div class="actual-data">
                <div class="actual-data-col bold">
                    <span>{{ 'USER-EDIT-REPORT.FIRST-NAME' | translate }}</span>
                    <span>{{ 'USER-EDIT-REPORT.LAST-NAME' | translate }}</span>
                    <span>{{ 'USER-EDIT-REPORT.E-MAIL' | translate }}</span>
                    <span>{{ 'USER-EDIT-REPORT.PHONE' | translate }}</span>
                </div>
                <div *ngIf="userData$ | async; let userData" class="actual-data-col">
                    <span>{{ userData.firstname }} </span>
                    <span>{{ userData.lastname }}</span>
                    <span>{{ userData.email }}</span>
                    <span>{{ userData.phone }}</span>
                </div>
            </div>
        </mat-tab>
        <mat-tab label="{{ 'USER-EDIT-REPORT.CHANGE-HISTORY' | translate }}">
            <mat-accordion [multi]="false">
                <mat-expansion-panel *ngFor="let change of modificationHistoryChanges?.results; let i = index"
                                     (opened)="panelOpenState = true"
                                     (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <ng-container>
                                {{ change['ModificationHistoryChange']['created'] | date: 'medium': '': 'pl'}}
                            </ng-container>
                        </mat-panel-title>
                        <mat-panel-description>
                            <ng-container *ngIf="userData$ | async as userData">
                                <span>{{ (userData.firstname || '') + ' ' + (userData.lastname || '') }}</span>
                            </ng-container>
                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <app-change-history [historyChanges]="modificationHistoryChanges?.results[i]"></app-change-history>
                </mat-expansion-panel>
            </mat-accordion>
        </mat-tab>
        <mat-tab label="{{ 'USER-EDIT-REPORT.SHARING-HISTORY' | translate }}">
        </mat-tab>
        <mat-tab label="{{ 'USER-EDIT-REPORT.PROTEST-HISTORY' | translate }}">
        </mat-tab>
        <mat-tab label="{{ 'USER-EDIT-REPORT.OTHER' | translate }}">
        </mat-tab>
    </mat-tab-group>
</div>
