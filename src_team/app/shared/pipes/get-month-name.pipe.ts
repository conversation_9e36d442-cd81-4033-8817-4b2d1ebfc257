import {Pipe, PipeTransform} from '@angular/core';

@Pipe({ name: 'getMonthName' })
export class GetMonthNamePipe implements PipeTransform {

    monthsName = ['Styczeń', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>źd<PERSON><PERSON>', 'Listopad', '<PERSON><PERSON><PERSON><PERSON>'];

    transform(value: any, args?: any): any {
        const index = value - 1;

        return this.monthsName[index];
    }

}
