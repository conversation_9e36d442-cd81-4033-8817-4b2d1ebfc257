<mat-toolbar role="toolbar" class="issue-toolbar" [ngClass]="(isHandset$ | async) ? 'mobile' : ''" ngClass.md="smaller" ngClass.lg="smaller">
    <button mat-icon-button matTooltip="{{'ISSUE-VIEW-MENU.BACK-TO-LIST' | translate}}" (click)="clickBack()" color="primary">
        <mat-icon class="navigate-before">navigate_before</mat-icon>
    </button>
    <span class="spacer"></span>
    <div class="button-row" *ngIf="issueData.status !== 'closed'; else isClosed" fxLayout fxLayoutAlign="center center">
        <ng-container *ngIf="!(isHandset$ | async); else buttonsMobile">
            <button class="transparent-button blue" appIssueActionButton="takeIssue" [issue]="issueData" [hideWhenNotAllowed]="true" mat-button (click)="clickTake()">
                <mat-icon>playlist_add</mat-icon>
                {{'ISSUE-VIEW-MENU.TAKE-ISSUE' | translate}}
            </button>
            <button
                class="transparent-button blue"
                appIssueActionButton="delegateIssue"
                [issue]="issueData"
                [hideWhenNotAllowed]="true"
                mat-button
                (click)="clickDelegate($event)"
                *ngIf="'delegateIssue' | checkPermissionName"
            >
                <mat-icon>turn_right</mat-icon>
                {{'ISSUE-VIEW-MENU.DELEGATE-ISSUE' | translate}}
            </button>
            <button
                *ngIf="+issueData.priority === 0"
                appIssueActionButton="setPriority"
                [issue]="issueData"
                [hideWhenNotAllowed]="true"
                mat-button
                (click)="clickAddPriority()"
                class="transparent-button blue"
            >
                <mat-icon>priority_high</mat-icon>
                {{'ISSUE-VIEW-MENU.SET-PRIORITY' | translate}}
            </button>
            <button
                *ngIf="+issueData.priority === 1"
                appIssueActionButton="setPriority"
                [issue]="issueData"
                [hideWhenNotAllowed]="true"
                mat-button
                (click)="clickDelPriority()"
                class="transparent-button blue"
            >
                <mat-icon>low_priority</mat-icon>
                {{'ISSUE-VIEW-MENU.DELETE-PRIORITY' | translate}}
            </button>
            <button *ngIf="'closeIssue' | checkPermissionName" class="transparent-button warn" appIssueActionButton="closeIssue" [issue]="issueData" [hideWhenNotAllowed]="false" mat-button (click)="clickClose()">
                <mat-icon>close</mat-icon>
                {{'ISSUE-VIEW-MENU.CLOSE-ISSUE' | translate}}
            </button>

            <mat-menu #issueMoreMenu="matMenu" class="no-max-width">
                <button mat-menu-item (click)="exportPdf()"><mat-icon>cloud_download</mat-icon>{{'ISSUE-VIEW-MENU.DOWNLOAD-ISSUE' | translate}}</button>
                <button
                    *ngIf="issueActionAllowedService.isActionAllowed('convertSubscriptionIssueToPaid', issueData)"
                    mat-menu-item
                    (click)="convertIssueToPaid()"
                >
                    <mat-icon>transform</mat-icon>{{'ISSUE-VIEW-MENU.CONVERT' | translate}}
                </button>
            </mat-menu>
            <button mat-icon-button [matMenuTriggerFor]="issueMoreMenu" color="primary">
                <mat-icon>more_vert</mat-icon>
            </button>
        </ng-container>

        <ng-template #buttonsMobile>
            <mat-slide-toggle
                color="primary"
                [checked]="showLogs"
                (change)="setShowLogs(!showLogs)"
            >
            </mat-slide-toggle>
            <button
                appIssueActionButton="valuateIssue"
                *ngIf="isVerifiedUser"
                [issue]="issueData"
                [hideWhenNotAllowed]="true"
                mat-icon-button
                (click)="environment.simplifyPricing ? addSimplifiedOffer() : valuateIssue()"
            >
                <mat-icon>monetization_on</mat-icon>
            </button>

            <button appIssueActionButton="takeIssue" [issue]="issueData" [hideWhenNotAllowed]="true" mat-icon-button (click)="clickTake()">
                <mat-icon>playlist_add</mat-icon>
            </button>
            <button appIssueActionButton="delegateIssue" [issue]="issueData" mat-icon-button (click)="clickDelegate($event)">
                <mat-icon>turn_right</mat-icon>
            </button>
            <button
                *ngIf="+issueData.priority === 0"
                appIssueActionButton="setPriority"
                [issue]="issueData"
                [hideWhenNotAllowed]="true"
                mat-icon-button
                (click)="clickAddPriority()"
            >
                <i-lucide name="alert-triangle"></i-lucide>
            </button>
            <button
                *ngIf="+issueData.priority === 1"
                appIssueActionButton="setPriority"
                [issue]="issueData"
                [hideWhenNotAllowed]="true"
                mat-icon-button
                (click)="clickDelPriority()"
            >
                <i-lucide name="alert-triangle-off"></i-lucide>
            </button>
            <button appIssueActionButton="closeIssue" [issue]="issueData" mat-icon-button (click)="clickClose()" color="accent">
                <i-lucide name="x"></i-lucide>
            </button>

            <mat-menu #issueMoreMenu="matMenu">
                <button mat-menu-item (click)="exportPdf()"><mat-icon>cloud_download</mat-icon>{{'ISSUE-VIEW-MENU.DOWNLOAD-ISSUE' | translate}}</button>
                <button
                    *ngIf="issueActionAllowedService.isActionAllowed('convertSubscriptionIssueToPaid', issueData)"
                    mat-menu-item
                    (click)="convertIssueToPaid()"
                >
                    <mat-icon>transform</mat-icon>{{'ISSUE-VIEW-MENU.CONVERT' | translate}}
                </button>
            </mat-menu>
            <button mat-icon-button [matMenuTriggerFor]="issueMoreMenu">
                <mat-icon>more_vert</mat-icon>
            </button>
        </ng-template>
    </div>
</mat-toolbar>

<ng-template #isClosed>
    <mat-slide-toggle
        color="primary"
        [checked]="showLogs"
        (change)="setShowLogs(!showLogs)"
    >{{'ISSUE-VIEW-MENU.LOGS' | translate}}
    </mat-slide-toggle>
    <button mat-button (click)="clickOpen()" appIssueActionButton="openIssue" [issue]="issueData" [hideWhenNotAllowed]="true">
        <mat-icon>done</mat-icon>
        {{'ISSUE-VIEW-MENU.OPEN-ISSUE' | translate}}
    </button>

    <mat-menu #issueMoreMenu="matMenu">
        <button mat-menu-item (click)="exportPdf()"><mat-icon>cloud_download</mat-icon>Pobierz sprawę do pliku PDF</button>
        <button
            *ngIf="issueActionAllowedService.isActionAllowed('convertSubscriptionIssueToPaid', issueData)"
            mat-menu-item
            (click)="convertIssueToPaid()"
        >
            <mat-icon>transform</mat-icon>{{'ISSUE-VIEW-MENU.CONVERT' | translate}}
        </button>
    </mat-menu>
    <button mat-icon-button [matMenuTriggerFor]="issueMoreMenu">
        <mat-icon>more_vert</mat-icon>
    </button>
</ng-template>

<ng-template #priorityAction>

</ng-template >
