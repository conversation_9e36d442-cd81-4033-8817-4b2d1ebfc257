<div class="container" [class.visible]="isOpen">
    <div class="container__content">
        <div class="container__content__header">
            <span class="container__content__header__title">{{'ISSUE-VIEW-SIDEBAR.COMMENTS' | translate}}</span>

            <div (click)="close()">
                <i-lucide name="x" class="close-icon"></i-lucide>
            </div>
        </div>

        <div class="container__content__separator"></div>

        <ng-container *ngFor="let groupId of getUniqueGroupIds()">
            <div class="container__content__comments__comment"
                 *ngFor="let comment of getCommentsForGroup(groupId); let i = index; let last = last"
                 [ngClass]="{
                    'reply-comment': i > 0,
                    'selected': selectedGroupId === comment.Comment.group_id.toString()
                 }"
                 (click)="selectCommentGroup(comment.Comment.group_id)"
                 [attr.data-id]="comment.Comment.group_id">
                <div class="container__content__comments__comment__header">
                    <span class="container__content__comments__comment__header__name">{{comment.User.firstname + ' ' + comment.User.lastname}}</span>
                    <span class="container__content__comments__comment__header__date">{{comment.Comment.created | dateRespondoFormat}}</span>
                </div>

                <div [innerHTML]="comment.Comment.body | linkify" class="container__content__comments__comment__body"></div>

                <div class="reply-icon-container"
                     *ngIf="last && activeReplyIndex !== getCommentIndex(comment.Comment.id)"
                     (click)="toggleReplyInput($event, getCommentIndex(comment.Comment.id), comment.Comment.group_id, comment.Comment.id)">
                    <i-lucide name='reply' class="reply-icon"></i-lucide>
                </div>

                <div class="reply-input-container" *ngIf="activeReplyIndex === getCommentIndex(comment.Comment.id)">
                    <textarea class="reply-input" [(ngModel)]="replyText" placeholder="{{'ISSUE-VIEW-SIDEBAR.REPLY-COMMENT' | translate}}"></textarea>
                    <div class="reply-actions">
                        <app-5ways-button appLucideIcons iconLeft="reply" [variant]="ButtonVariant.SECONDARY" class="submit-btn" (click)="submitReply($event, comment.Comment.group_id)">{{'ISSUE-VIEW-SIDEBAR.ANSWER' | translate}}</app-5ways-button>
                        <app-5ways-button appLucideIcons iconLeft="x" [variant]="ButtonVariant.SECONDARY" class="cancel-btn" (click)="cancelReply($event)">{{'ISSUE-VIEW-SIDEBAR.CANCEL' | translate}}</app-5ways-button>
                    </div>
                </div>
            </div>

            <div class="container__content__separator"></div>
        </ng-container>

    </div>
</div>
