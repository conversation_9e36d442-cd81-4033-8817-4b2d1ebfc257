import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {IssueService} from '../../../services/issue.service';
import {Subscription} from 'rxjs';
import {DashboardService} from '../../../services/dashboard.service';
import { HttpParams } from '@angular/common/http';
import {IssueStatus} from '../../../common/enums/issue-status.enum';
import { NgIf } from '@angular/common';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-unassigned-issues',
    templateUrl: './unassigned-issues.component.html',
    styleUrls: ['./unassigned-issues.component.scss'],
    imports: [NgIf, TranslatePipe]
})
export class UnassignedIssuesComponent implements OnInit, OnDestroy {
    unassignedIssues: number = 0;
    unassignedIssuesPreview: number = 10;
    @Input() isModuleVisible: boolean = false;

    private _issuesUnassignedSubscription: Subscription;

    constructor(private _issueService: IssueService) {
    }

    ngOnInit() {
        this.getUnassignedIssues();
    }

    getUnassignedIssues() {
        const params: HttpParams = new HttpParams()
            .set('status', `${IssueStatus.OPEN},${IssueStatus.NEW}`)
            .set('owner_id', 'null')
            .set('order', 'id')
            .set('limit', '1');

        this._issuesUnassignedSubscription = this._issueService.getIssues(params)
            .subscribe(data => {
                this.unassignedIssues = data.total;
            });
    }

    ngOnDestroy() {
        this._issuesUnassignedSubscription?.unsubscribe();
    }
}
