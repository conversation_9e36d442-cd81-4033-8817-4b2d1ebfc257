import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {environment} from '../../environments/environment';
import {DataResponseInterface} from '../common/interfaces/data-response.interface';

@Injectable({
    providedIn: 'root'
})
export class ClientWfirmaContractorService {
    endpointUrl = environment.apiUrl + 'contractor_data';

    constructor(private http: HttpClient) {
    }

    getContractors(userClientId: string): Observable<DataResponseInterface[]> {
        const params = new HttpParams().set('user_client_id', userClientId);

        return this.http.get<DataResponseInterface>(this.endpointUrl, { params }).pipe(
            map((response) => response.results.map((result) => result.ContractorData))
        );
    }
}
