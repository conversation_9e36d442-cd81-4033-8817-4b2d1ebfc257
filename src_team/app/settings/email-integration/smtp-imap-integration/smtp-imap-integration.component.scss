@use '../../../../variables' as *;

.action-btns {
    gap: 10px;
}

.button-container {
    margin-top: 30px;
}

.w-60 {
    display: flex;
    justify-content: space-between;

    app-5ways-button {
        margin-right: 10px;

        &:last-child {
            margin-right: 0;
        }
    }
}

.send-msg {
    margin-top: -8px;
}

.calculated-width {
    width: calc(60% + 20px);

    .mailbox-label {
        display: block;
        margin-bottom: 15px;
        font-weight: 600;
        font-size: 16px;
        color: $fiveways-gray;
    }

    mat-chip-listbox {
        margin-bottom: 20px;
    }
}

.sync-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    margin-bottom: 25px;

    .email-address {
        font-size: 14px;

        strong {
            font-weight: 600;
        }
    }
}

.readonly-input {
    color: $fiveways-gray-80;

    input {
        cursor: not-allowed;
    }
}

form {
    margin-bottom: 20px;

    div {
        margin-bottom: 8px;
    }

    mat-form-field {
        margin-bottom: 8px;
    }

    .section-header {
        font-weight: 600;
        font-size: 16px;
        margin-top: 20px;
        margin-bottom: 15px;
        color: $fiveways-gray;
    }
}

mat-card-title {
    margin-bottom: 20px;
}

.d-flex.row {
    margin-bottom: 5px;
}
