import { Component, OnInit } from '@angular/core';
import {Mat<PERSON><PERSON>ogActions, MatDialogContent, MatDialogRef, MatDialogTitle} from '@angular/material/dialog';
import {FormBuilder, FormGroup, Validators, AbstractControl, ReactiveFormsModule} from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {debounceTime, finalize} from 'rxjs/operators';
import {GusService} from '../../../services/gus.service';
import {MessagingService} from '../../../services/messaging.service';
import {LoadingStatus} from '../../../common/enums/loading-status.enum';
import {ButtonVariant} from '../../../common/enums/button-variant.enum';
import {RespondoValidators} from '../../../shared/validators/RespondoValidators';
import { ClientNipInputAction } from '../../../common/enums/client-nip-input-action.enum';
import {ButtonComponent} from '../../../elements/button/button.component';
import {MatFormField, MatHint, MatLabel} from '@angular/material/select';
import {MatProgressSpinner} from '@angular/material/progress-spinner';
import {MatInput} from '@angular/material/input';
import {NgIf} from '@angular/common';

@Component({
  selector: 'app-client-nip-input',
  templateUrl: './client-nip-input.component.html',
  styleUrls: ['./client-nip-input.component.scss'],
    imports: [
        MatDialogTitle,
        MatDialogContent,
        TranslatePipe,
        ReactiveFormsModule,
        MatFormField,
        MatProgressSpinner,
        MatDialogActions,
        ButtonComponent,
        MatInput,
        MatHint,
        MatLabel,
        NgIf
    ]
})
export class ClientNipInputComponent implements OnInit {
  nipForm: FormGroup;
  loadingStatus = LoadingStatus.loaded;
  readonly loadingStatusValues = LoadingStatus;
  readonly ButtonVariant = ButtonVariant;
  // Kody klawiszy dla cyfr 0-9
  private readonly KEY_CODE_DIGIT_0 = 48;
  private readonly KEY_CODE_DIGIT_9 = 57;

  formSubmitted = false;

  constructor(
    public dialogRef: MatDialogRef<ClientNipInputComponent>,
    private fb: FormBuilder,
    private gusService: GusService,
    private translate: TranslateService,
    private messagingService: MessagingService
  ) { }

  ngOnInit(): void {
    this.initForm();

    this.nipForm.get('nip').valueChanges
      .pipe(
        debounceTime(500)
      )
      .subscribe(value => {
        const cleanedValue = this.cleanNip(value);
        if (value && value !== cleanedValue) {
          this.nipForm.get('nip').setValue(cleanedValue, { emitEvent: false });
          value = cleanedValue;
        }

        if (this.nipForm.valid && value) {
          this.fetchGusData();
        }
      });
  }

  private initForm(): void {
    this.nipForm = this.fb.group({
      nip: ['', [
        Validators.minLength(10),
        Validators.maxLength(10),
        RespondoValidators.nipFormat
      ]]
    });
  }

  get nipControl(): AbstractControl {
    return this.nipForm.get('nip');
  }

  onCancel(): void {
    this.dialogRef.close({ action: ClientNipInputAction.CANCEL });
  }

  onManualFill(): void {
    this.formSubmitted = false;
    this.dialogRef.close({ action: ClientNipInputAction.MANUAL });
  }

  onNipPaste(event: ClipboardEvent): void {
    event.preventDefault();

    const clipboardData = event.clipboardData;
    let pastedText = clipboardData.getData('text');

    pastedText = this.cleanNip(pastedText);

    pastedText = pastedText.substring(0, 10);

    this.nipForm.get('nip').setValue(pastedText);
  }

  onNipInput(): void {
    const nipControl = this.nipForm.get('nip');
    const currentValue = nipControl.value;

    const cleanedValue = this.cleanNip(currentValue);
    if (currentValue && currentValue !== cleanedValue) {
      nipControl.setValue(cleanedValue, { emitEvent: false });
    }
  }

  onNipKeyPress(event: KeyboardEvent): void {
    const keyCode = event.keyCode || event.which;
    if (keyCode < this.KEY_CODE_DIGIT_0 || keyCode > this.KEY_CODE_DIGIT_9) {
      event.preventDefault();
    }
  }

  fetchGusData(): void {
    this.formSubmitted = true;
    this.nipForm.markAllAsTouched();

    const nipValue = this.nipForm.get('nip').value;

    if (!nipValue || this.nipForm.invalid) {
      return;
    }

    const nip = this.cleanNip(this.nipForm.get('nip').value);

    this.nipForm.get('nip').setValue(nip, { emitEvent: false });
    this.loadingStatus = LoadingStatus.loading;

    this.gusService.getCompanyData(nip)
      .pipe(
        finalize(() => this.loadingStatus = LoadingStatus.loaded)
      )
      .subscribe(
        companyData => {
          this.dialogRef.close({
            action: ClientNipInputAction.SUCCESS,
            data: {
              name: companyData.name,
              nip: nip,
              city: companyData.city,
              street: companyData.street,
              zip: companyData.zip
            }
          });
        },
        (error: HttpErrorResponse) => {
          this.messagingService.showError(this.translate.instant('CONTRACTORS.GUS-ERROR-NIP'));
        }
      );
  }

  /**
   * Usuwa myślniki i spacje z numeru NIP
   */
  private cleanNip(nip: string): string {
    return nip ? nip.replace(/[\s-]/g, '') : nip;
  }
}
